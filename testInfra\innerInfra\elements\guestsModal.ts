import { expect, Locator } from '@playwright/test';
import Button from './button';
import { BaseElement } from './baseElement';
import GuestsModalElements from '../../interfaces/guestsModalElements';
import { GuestsInfo } from '../../interfaces/guestsInfo';

export default class GuestsModal extends BaseElement {
  readonly closeButton: Button;

  private readonly stepperAdultsIncreaseButton: Button;
  private readonly stepperChildrenIncreaseButton: Button;
  private readonly stepperAdultsDecreaseButton: Button;
  private readonly stepperChildrenDecreaseButton: Button;

  // Create VisualCue class
  private readonly modalAdultsHeader: Button;
  private readonly readonlyAdultCount: Locator;
  private readonly childrenCount: Locator;

  constructor(
    openButton: Locator,
    {
      modalAdultsHeader,
      adultsIncreaseLocator,
      childrenIncreaseLocator,
      adultsDecreaseLocator,
      childrenDecreaseLocator,
      adultCountLocator,
      childrenCountLocator,
    }: GuestsModalElements
  ) {
    super(openButton);

    this.modalAdultsHeader = new Button(modalAdultsHeader);
    this.stepperAdultsIncreaseButton = new Button(adultsIncreaseLocator);
    this.stepperChildrenIncreaseButton = new Button(childrenIncreaseLocator);
    this.stepperAdultsDecreaseButton = new Button(adultsDecreaseLocator);
    this.stepperChildrenDecreaseButton = new Button(childrenDecreaseLocator);
    this.closeButton = new Button(this.page.getByRole('button', { name: 'Close' }));
    this.readonlyAdultCount = adultCountLocator;
    this.childrenCount = childrenCountLocator;
  }

  async selectNumberOfGuests({
    guestsInfo,
    shouldCloseModal = true,
  }: {
    guestsInfo: GuestsInfo;
    shouldCloseModal?: boolean;
  }) {
    await this.click();

    // TODO: Use Enum?
    await this.modalAdultsHeader.isVisible({ shouldThrow: false });
    await this.adjustGuestCount('adults', guestsInfo.numberOfAdults);
    await this.adjustGuestCount('children', guestsInfo.numberOfChildren);

    if (shouldCloseModal) {
      await this.closeButton.click();
    }
  }

  private async adjustGuestCount(guestType: 'adults' | 'children', targetCount: number) {
    const currentCount = await this.getGuestCount(guestType);

    if (currentCount > targetCount) {
      await this.decreaseGuestCount(guestType, targetCount, currentCount);
    } else if (currentCount < targetCount) {
      await this.increaseGuestCount(guestType, targetCount, currentCount);
    }
  }

  private async getGuestCount(guestType: 'adults' | 'children'): Promise<number> {
    const countElement = guestType === 'adults' ? this.readonlyAdultCount : this.childrenCount;
    return Number(await countElement.textContent());
  }

  private async decreaseGuestCount(
    guestType: 'adults' | 'children',
    targetCount: number,
    currentCount: number
  ) {
    const decreaseButton =
      guestType === 'adults'
        ? this.stepperAdultsDecreaseButton
        : this.stepperChildrenDecreaseButton;

    const countElement = guestType === 'adults' ? this.readonlyAdultCount : this.childrenCount;

    for (let i = 0; i < currentCount - targetCount; i++) {
      await decreaseButton.click();
    }

    expect(countElement).toContainText(targetCount.toString());
  }

  private async increaseGuestCount(
    guestType: 'adults' | 'children',
    targetCount: number,
    currentCount: number
  ) {
    const increaseButton =
      guestType === 'adults'
        ? this.stepperAdultsIncreaseButton
        : this.stepperChildrenIncreaseButton;

    const countElement = guestType === 'adults' ? this.readonlyAdultCount : this.childrenCount;

    for (let i = 0; i < targetCount - currentCount; i++) {
      await increaseButton.click();
    }

    expect(countElement).toContainText(targetCount.toString());
  }
}
