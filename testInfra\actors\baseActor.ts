import { Page } from '@playwright/test';

// There is no need for this class in a project at this size,
// But in a bigger project there will also be admin users, listers, etc each will have different actions
// and common actions will be here(ie: navigate to)
export abstract class BaseActor {
  protected page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  get currentUrl() {
    return this.page.url();
  }
}
