import { Page } from '@playwright/test';
import { BaseActor } from './baseActor';
import { StayDates } from '../interfaces/stayDates';
import { HomePage } from '../innerInfra/pages/HomePage';
import { GuestsInfo } from '../interfaces/guestsInfo';
import { SearchResultsPage } from '../innerInfra/pages/searchResultsPage';
import { ListingDetailsPage } from '../innerInfra/pages/listingDetailsPage';
import { ReserveConfirmationPage } from '../innerInfra/pages/reserveConfirmationPage';

export class RenterActor extends BaseActor {
  constructor(page: Page) {
    super(page);
  }

  async searchForListing({
    destination,
    dates,
    guestsInfo,
  }: {
    destination: string;
    dates: StayDates;
    guestsInfo: GuestsInfo;
  }) {
    const homePage = new HomePage(this.page);
    await homePage.navigate();
    await homePage.destinationInput.fill({ text: destination });
    await homePage.dateSelector.selectDate({ dates });
    await homePage.addGuestsModal.selectNumberOfGuests({ guestsInfo });
    await homePage.searchButton.click();

    return new SearchResultsPage(this.page);
  }

  async selectListingAfterSearchWithHighestRating() {
    const searchResultsPage = new SearchResultsPage(this.page);
    const highestRatedListing = await searchResultsPage.getListingWithHighestRating();
    const page1 = await highestRatedListing.click();

    const listingDetailsPage = new ListingDetailsPage(page1);
    await listingDetailsPage.closeTranslationPopupButton.click();

    // Add Assertions for listing details page

    return listingDetailsPage;
  }

  async updateDatesAndGuestsInfoOnListingPage({
    dates,
    guestsInfo,
  }: {
    dates: StayDates;
    guestsInfo: GuestsInfo;
  }) {
    const listingDetailsPage = new ListingDetailsPage(this.page);
    const wasUpdatedSuccesfully = await listingDetailsPage.dateSelector.selectDate({
      dates,
    });

    await listingDetailsPage.changeGuestsModal.selectNumberOfGuests({
      guestsInfo,
    });

    return wasUpdatedSuccesfully;
  }

  async reserveListing() {
    const listingDetailsPage = new ListingDetailsPage(this.page);
    await listingDetailsPage.reserveButton.click();

    return new ReserveConfirmationPage(this.page);
  }
}
