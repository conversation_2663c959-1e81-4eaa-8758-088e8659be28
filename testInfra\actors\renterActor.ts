import { Page } from '@playwright/test';
import { BaseActor } from './baseActor';
import { StayDates } from '../interfaces/stayDates';
import { HomePage } from '../innerInfra/pages/HomePage';
import { GuestsInfo } from '../interfaces/guestsInfo';
import { SearchResultsPage } from '../innerInfra/pages/searchResultsPage';
import { ListingDetailsPage } from '../innerInfra/pages/listingDetailsPage';
import { ReserveConfirmationPage } from '../innerInfra/pages/reserveConfirmationPage';
import { TestStep } from '../testStepDecorator';

export class RenterActor extends BaseActor {
  constructor(page: Page) {
    super(page);
  }

  @TestStep('Search for listing with all needed info')
  async searchForListing({
    destination,
    dates,
    guestsInfo,
  }: {
    destination: string;
    dates: StayDates;
    guestsInfo: GuestsInfo;
  }) {
    const homePage = new HomePage(this.page);
    await homePage.navigate();
    await homePage.destinationInput.fill({ text: destination });
    await homePage.dateSelector.selectDate({ dates, shouldCloseModal: false });
    await homePage.addGuestsModal.selectNumberOfGuests({ guestsInfo, shouldCloseModal: false });
    await homePage.searchButton.click();

    return new SearchResultsPage(this.page);
  }

  @TestStep('Select listing with highest rating from search results')
  async selectListinghWithHighestRating() {
    const searchResultsPage = new SearchResultsPage(this.page);
    const highestRatedListing = await searchResultsPage.getListingWithHighestRating();
    const page1 = await highestRatedListing.click();

    const listingDetailsPage = new ListingDetailsPage(page1);
    await listingDetailsPage.closeTranslationPopupButton.click();

    this.setPage(page1);

    // Add Assertions for listing details page

    return listingDetailsPage;
  }

  @TestStep('Update dates and guests info on listing page')
  async updateRequestedListingInfo({
    dates,
    guestsInfo,
    shouldReserveListing = false,
  }: {
    dates: StayDates;
    guestsInfo: GuestsInfo;
    shouldReserveListing?: boolean;
  }) {
    const listingDetailsPage = new ListingDetailsPage(this.page);
    const wasUpdatedSuccesfully = await listingDetailsPage.dateSelector.selectDate({
      dates,
    });

    await listingDetailsPage.changeGuestsModal.selectNumberOfGuests({
      guestsInfo,
      shouldCloseModal: shouldReserveListing,
    });

    if (shouldReserveListing) {
      await this.reserveListing();
    }

    return wasUpdatedSuccesfully;
  }

  @TestStep('Reserve the selected listing')
  async reserveListing() {
    const listingDetailsPage = new ListingDetailsPage(this.page);
    await listingDetailsPage.reserveButton.click();

    return new ReserveConfirmationPage(this.page);
  }
}
