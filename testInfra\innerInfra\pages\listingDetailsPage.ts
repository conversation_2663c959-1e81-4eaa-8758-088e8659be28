import { Page } from '@playwright/test';
import { ArbnbBasePage } from './arbnbBasePage';
import Button from '../elements/button';
import DateSelectorModal from '../elements/dateSelectorModal';
import GuestsModal from '../elements/guestsModal';

export class ListingDetailsPage extends ArbnbBasePage {
  readonly changeGuestsModal: GuestsModal;
  readonly reserveButton: Button;
  readonly dateSelector: DateSelectorModal;
  readonly closeTranslationPopupButton: Button;

  constructor(page: Page) {
    super(page);

    const closeButtonLocator = this.page.getByRole('button', { name: 'Close' });

    //TODO: make sure all locators are correct
    this.changeGuestsModal = new GuestsModal(this.page.locator('#GuestPicker-book_it-trigger'), {
      modalAdultsHeader: this.page.getByText('Adults', { exact: true }),
      adultsIncreaseLocator: this.page.getByTestId(
        'GuestPicker-book_it-form-stepper-adults-increase-button'
      ),
      childrenIncreaseLocator: this.page.getByTestId(
        'GuestPicker-book_it-form-stepper-children-increase-button'
      ),
      adultsDecreaseLocator: this.page.getByTestId(
        'GuestPicker-book_it-form-stepper-adults-decrease-button'
      ),
      childrenDecreaseLocator: this.page.getByTestId(
        'GuestPicker-book_it-form-children-stepper-decrease-button'
      ),
      adultCountLocator: this.page.getByTestId('GuestPicker-book_it-form-adults-stepper-value'),
      childrenCountLocator: this.page.getByTestId(
        'GuestPicker-book_it-form-children-stepper-value'
      ),
    });

    this.closeTranslationPopupButton = new Button(closeButtonLocator);

    this.reserveButton = new Button(this.page.getByRole('button', { name: 'Reserve' }));

    this.dateSelector = new DateSelectorModal(
      this.page.getByRole('button', { name: 'Change dates; Check-in:', exact: false }),
      this.page.getByTestId('change-dates-checkIn'),
      this.page.getByTestId('change-dates-checkOut'),
      this.page.getByTestId('bookit-sidebar-availability-calendar'),
      closeButtonLocator
    );
  }
}
