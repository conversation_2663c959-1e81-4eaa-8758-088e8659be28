import { Page } from '@playwright/test';
import { ArbnbBasePage } from './arbnbBasePage';
import But<PERSON> from '../elements/button';
import DateSelectorModal from '../elements/dateSelector';
import TextAreaWithOptions from '../elements/selectList';
import AddGuestsModal from '../elements/addGuestsModal';

export class HomePage extends ArbnbBasePage {
  // TODO: Improve waiting to reduce chance of flakiness on selection
  readonly dateSelector: DateSelectorModal;
  readonly destinationInput: TextAreaWithOptions;
  readonly searchButton: Button;
  readonly addGuestsModal: AddGuestsModal;

  constructor(page: Page) {
    super(page);

    this.destinationInput = new TextAreaWithOptions(
      this.page.getByTestId('structured-search-input-field-query'),
      this.page.locator('[data-testid^="option"]:visible')
    );

    this.dateSelector = new DateSelectorModal(
      page.getByRole('tab', { name: 'Dates' }),
      this.page.getByRole('button', { name: 'Check in Add dates' }),
      this.page.getByRole('button', { name: 'Check out Add dates' }),
      this.page.locator('.ptpmekl').first(),
      this.page.getByRole('button', { name: 'Close' })
    );

    this.addGuestsModal = new AddGuestsModal(
      this.page.getByRole('button', { name: 'Who Add guests' }),
      {
        modalAdultsHeader: this.page.getByRole('heading', { name: 'Adults' }),
        adultsIncreaseLocator: this.page.getByTestId('stepper-adults-increase-button').first(),
        childrenIncreaseLocator: this.page.getByTestId('stepper-children-increase-button').first(),
        adultsDecreaseLocator: this.page.getByTestId('stepper-adults-decrease-button').first(),
        childrenDecreaseLocator: this.page.getByTestId('stepper-children-decrease-button').first(),
        adultCountLocator: this.page.getByTestId('stepper-adults-value').first(),
        childrenCountLocator: this.page.getByTestId('stepper-children-value').first(),
      }
    );

    this.searchButton = new Button(this.page.getByTestId('structured-search-input-search-button'));
  }
}
