import { Locator, Page } from '@playwright/test';
import { ArbnbBasePage } from './arbnbBasePage';
import { ListingCard } from '../elements/listingCard';

export class SearchResultsPage extends ArbnbBasePage {
  private readonly resultsCardLocator: Locator;

  constructor(page: Page) {
    super(page);

    this.resultsCardLocator = page.getByTestId('card-container');
  }

  override async navigate() {
    await super.navigate();

    await this.page.getByTestId('card-container').nth(10).waitFor();
  }

  async getAllListings({
    minAmountOfListings = 10,
  }: { minAmountOfListings?: number } = {}): Promise<ListingCard[]> {
    await this.resultsCardLocator.nth(minAmountOfListings - 1).waitFor();

    return (await this.resultsCardLocator.all()).map((locator) => new ListingCard(locator));
  }

  async getListingWithHighestRating(): Promise<ListingCard> {
    //TODO: break into smaller methods
    const allCards = await this.getAllListings();

    let highestRating = 0;
    let cardWithHighestRating: ListingCard | undefined;

    for (const card of allCards) {
      const text = await card.textContent();
      const match = text?.match(/reviews\s*([\d.]+)\s*\(/);
      const rating = match ? parseFloat(match[1]) : 0;

      if (rating > highestRating) {
        highestRating = rating;
        cardWithHighestRating = card;
      }
    }

    console.log(`Highest rating: ${highestRating}`);

    if (cardWithHighestRating) {
      return cardWithHighestRating;
    }

    throw new Error('No listings were found');
  }
}
