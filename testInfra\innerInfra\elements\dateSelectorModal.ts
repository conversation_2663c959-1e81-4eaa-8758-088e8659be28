import { Locator } from '@playwright/test';
import { BaseElement } from './baseElement';
import { StayDates } from '../../interfaces/stayDates';
import { TestDataManager } from '../../testDataManager';
import Button from './button';

export default class DateSelectorModal extends BaseElement {
  private checkInLocator: Locator;
  private checkOutLocator: Locator;
  //Todo use visual cue
  private calanderLocator: Button;
  readonly closeButton: Button;

  constructor(
    locator: Locator,
    checkInLocator: Locator,
    checkOutLocator: Locator,
    calanderLocator: Locator,
    closeButtonLocator: Locator
  ) {
    super(locator);
    this.checkInLocator = checkInLocator;
    this.checkOutLocator = checkOutLocator;
    this.calanderLocator = new Button(calanderLocator);
    this.closeButton = new Button(closeButtonLocator);
  }

  /*
   * Selects a date range
   * Returns true if the date was selected successfully
   * Returns false if the date was not selected successfully
   */
  async selectDate({
    dates,
    shouldCloseModal = true,
  }: {
    dates: StayDates;
    shouldCloseModal?: boolean;
  }) {
    const wasUpdateSuccesful = await this._selectDate({
      dates,
      clickCheckInLocator: true,
    });

    if (shouldCloseModal) {
      await this.closeButton.click();
    }

    return wasUpdateSuccesful;
  }

  private async _selectDate({
    dates,
    clickCheckInLocator = true,
  }: {
    dates: StayDates;
    clickCheckInLocator?: boolean;
  }) {
    if (!dates.checkInDate && !dates.checkOutDate) {
      throw new Error('Invalid dates, both must be defined');
    }

    const isModalOpen = await this.calanderLocator.isVisible({ shouldThrow: false });
    let checkInDateButton: Button | undefined;
    let checkOutDateButton: Button | undefined;

    if (dates.checkInDate) {
      checkInDateButton = new Button(
        this.calanderLocator.locator.getByRole('button', {
          name: TestDataManager.formatDateForLocator({ date: dates.checkInDate }),
        })
      );
    }

    if (dates.checkOutDate) {
      checkOutDateButton = new Button(
        this.calanderLocator.locator.getByRole('button', {
          name: TestDataManager.formatDateForLocator({ date: dates.checkOutDate }),
        })
      );
    }

    if (clickCheckInLocator) {
      if (!isModalOpen) {
        await this.checkInLocator.click();
        console.log('Clicked check in locator and opened modal');
      }

      if (!(await checkInDateButton?.isClickable())) {
        console.log('Check in date button is not clickable');
        return false;
      }

      console.log('Clicked check in date button');
      await checkInDateButton!.click();

      if (!(await checkOutDateButton?.isClickable())) {
        console.log('Check out date button is not clickable');
        return false;
      }

      console.log('Clicked check out date button');
      await checkOutDateButton!.click();
    } else {
      if (!isModalOpen) {
        await this.checkOutLocator.click();
      }

      if (!(await checkOutDateButton?.isClickable())) {
        return false;
      }

      await checkOutDateButton!.click();
    }

    return true;
  }
}
