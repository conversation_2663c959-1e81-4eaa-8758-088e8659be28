import { Page } from '@playwright/test';

export abstract class BasePage {
  protected page: Page;
  protected url: string;

  constructor(page: Page, url?: string) {
    this.page = page;
    this.url = url ?? this.getInitalUrl();
  }

  getInitalUrl() {
    return this.url;
  }

  async navigate() {
    await this.page.goto(this.url);
    await this.page.getByTestId('card-container').nth(20).waitFor();
  }

  async getCurrentUrl() {
    return this.page.url();
  }
}
