import { test, expect } from '@playwright/test';
import { TestDataManager } from '../testInfra/testDataManager';
import { RenterActor } from '../testInfra/actors/renterActor';

// Flakiness notes:
// I am assuming the broswer is set in english, if it is not the tests will fail since the locators are based on element names
// Some properties may not allow to book for less than X days, if they are selected the test will fail
// On my search a automated translation appears(becuase it's looking for places in france, and they are in french)
test('Arbnb', async ({ page }) => {
  const renterActor = new RenterActor(page);
  const initalDates = TestDataManager.generateValidDates({
    amountOfDaysFromToday: 1,
    lengthOfStayInDays: 3,
  });
  const dateAfterSuccesfulUpdate = TestDataManager.generateValidDates({
    amountOfDaysFromToday: 7,
    lengthOfStayInDays: 3,
  });

  const initalGuestsInfo = {
    numberOfAdults: 2,
    numberOfChildren: 1,
  };

  const guestsInfoAfterUpdate = {
    numberOfAdults: 2,
    numberOfChildren: 0,
  };

  await renterActor.searchForListing({
    destination: 'Paris',
    dates: initalDates,
    guestsInfo: initalGuestsInfo,
  });

  await renterActor.selectListingAfterSearchWithHighestRating();

  const wasUpdatedSuccesfully = await renterActor.updateDatesAndGuestsInfoOnListingPage({
    dates: dateAfterSuccesfulUpdate,
    guestsInfo: guestsInfoAfterUpdate,
  });

  await renterActor.reserveListing();

  const expectedDates = wasUpdatedSuccesfully ? dateAfterSuccesfulUpdate : initalDates;

  const parsedUrl = TestDataManager.parseAirbnbBookingUrl({
    url: renterActor.currentUrl,
  });

  await expect.soft(parsedUrl.numberOfAdults).toBe(guestsInfoAfterUpdate.numberOfAdults);
  await expect.soft(parsedUrl.numberOfGuests).toBe(guestsInfoAfterUpdate.numberOfChildren);
  await expect
    .soft(parsedUrl.checkinDateString)
    .toBe(TestDataManager.convertDateToUrlString({ date: expectedDates?.checkInDate }));
  await expect
    .soft(parsedUrl.checkoutDateString)
    .toBe(TestDataManager.convertDateToUrlString({ date: expectedDates?.checkOutDate }));
});
