import { StayDates } from './interfaces/stayDates';

export class TestDataManager {
  // Static in general is not recommended because the risk of sharing state but:
  // - This class is a holder of stateless utility functions (no internal or shared state)
  // - Using `static` avoids unnecessary instantiation and makes the class usable as a utility
  // - No risk of race conditions, as JavaScript is single-threaded & <PERSON><PERSON> ensures tests run in isolation

  static generateValidDates({
    amountOfDaysFromToday = 1,
    lengthOfStayInDays = 1,
  }: {
    amountOfDaysFromToday: number;
    lengthOfStayInDays: number;
  }): StayDates {
    const checkInDate = new Date();
    checkInDate.setDate(checkInDate.getDate() + amountOfDaysFromToday);

    const checkOutDate = new Date(checkInDate);
    checkOutDate.setDate(checkOutDate.getDate() + lengthOfStayInDays);

    return { checkInDate, checkOutDate };
  }

  static formatDateForLocator = ({ date }: { date: Date }): string => {
    const day = date.getDate();
    const weekday = date.toLocaleDateString('en-US', { weekday: 'long' });
    const month = date.toLocaleDateString('en-US', { month: 'long' });
    const year = date.getFullYear();

    return `${day}, ${weekday}, ${month} ${year}.`;
  };

  static parseAirbnbBookingUrl({ url }: { url: string }) {
    const params = new URL(url).searchParams;

    return {
      numberOfAdults: Number(params.get('numberOfAdults') || 0),
      numberOfGuests: Number(params.get('numberOfGuests') || 0),
      checkin: params.get('checkin') || null,
      checkout: params.get('checkout') || null,
      numberOfChildren: Number(params.get('numberOfChildren') || 0),
      numberOfInfants: Number(params.get('numberOfInfants') || 0),
      numberOfPets: Number(params.get('numberOfPets') || 0),
      isWorkTrip: params.get('isWorkTrip') === 'true',
    };
  }
}
