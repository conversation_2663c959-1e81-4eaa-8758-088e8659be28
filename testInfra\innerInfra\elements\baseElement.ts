import { expect, Locator, <PERSON> } from '@playwright/test';

export abstract class BaseElement {
  protected _locator: Locator;
  protected page: Page;

  constructor(locator: Locator) {
    this._locator = locator;
    this.page = locator.page();
  }

  // We will use click in base(despite not every element is clickable)
  // To simulate users more reliabily if needed(users will click on non clickable elements)
  // And to allow for easy removal of popups/modals and element focusing to reduce change of flakiness
  // ie: User can click on nav bar to close a popup
  async click(): Promise<Page | void> {
    await this._locator.click();

    return this.page;
  }

  async textContent() {
    return await this._locator.textContent();
  }

  async isVisible({
    timeout = 5000,
    shouldThrow = true,
  }: { timeout?: number; shouldThrow?: boolean } = {}) {
    try {
      await expect(this._locator).toBeVisible({ timeout });

      return true;
    } catch (error) {
      if (shouldThrow) {
        throw error;
      }

      console.log(error.message);

      return false;
    }
  }

  get locator() {
    return this._locator;
  }
}
