import { Locator } from '@playwright/test';
import Text<PERSON>rea from './textArea';
import But<PERSON> from './button';

export default class TextAreaWithOptions extends TextArea {
  private optionsLocator: Locator;

  constructor(locator: Locator, options: Locator) {
    super(locator);
    this.optionsLocator = options;
  }

  async fill({
    text,
    shouldClickBeforeWriting = true,
  }: {
    text: string;
    shouldClickBeforeWriting?: boolean;
  }) {
    if (shouldClickBeforeWriting) {
      await this.click();
    }

    await this._locator.fill(text);
  }

  async selectOption(option: string) {
    await this.fill({ text: option });
    await this.optionsLocator.first().waitFor();

    const options: Button[] = (await this.optionsLocator.all()).map(
      (locator) => new Button(locator)
    );

    let optionFound = false;
    for (const button of options) {
      const buttonText = (await button.textContent()) ?? '';

      if (buttonText.includes(option)) {
        await button.click();
        optionFound = true;
      }
    }

    if (!optionFound) {
      throw new Error(
        `Option ${option} not selected because it was not contained in any of the options, found options are:\n${options}`
      );
    }
  }
}
