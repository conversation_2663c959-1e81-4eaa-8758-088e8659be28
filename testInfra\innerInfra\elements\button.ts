import { expect, Locator } from '@playwright/test';
import { BaseElement } from './baseElement';

export default class <PERSON><PERSON> extends BaseElement {
  constructor(locator: Locator) {
    super(locator);
  }

  override async click() {
    await this.isClickable({ shouldThrow: true });

    return await super.click();
  }

  async isClickable({
    shouldThrow = false,
    timeout = 5000,
  }: { shouldThrow?: boolean; timeout?: number } = {}) {
    try {
      console.log(`shouldThrow: ${shouldThrow}, timeout: ${timeout}`);
      await expect(this._locator).toBeEnabled({ timeout: timeout });
      await expect(this._locator).toBeVisible({ timeout: timeout });
      await expect(this._locator).toBeAttached({ timeout: timeout });
      await expect(this._locator).not.toHaveAttribute('data-is-day-blocked', 'true');

      return true;
    } catch (error) {
      if (shouldThrow) {
        throw error;
      }

      return false;
    }
  }
}
